import { useEffect, useRef } from 'react';

interface PrintCharacterCardProps {
  imageUrl: string;
  name: string;
  description: string;
  creator: string;
  likes: number;
  powerStat?: number;
  magicStat?: number;
  speedStat?: number;
}

// Pure HTML/CSS function that doesn't use React components
export function createPrintCardHTML({
  imageUrl,
  name,
  description,
  creator,
  likes,
  powerStat,
  magicStat,
  speedStat
}: PrintCharacterCardProps): string {
  return `
    <div style="width: 1200px; height: 1500px; margin: 0; padding: 0; font-family: ui-sans-serif, system-ui, sans-serif; background: #f3f4f6; display: flex; align-items: center; justify-content: center; box-sizing: border-box;">
      <!-- Trading Card positioned within 8mm safe area -->
      <div style="
        position: relative;
        background: transparent;
        padding: 20px;
        width: 450px;
        margin: 94px;
        box-sizing: border-box;
        overflow: visible;
      ">
        
        <!-- Inner card -->
        <div style="
          position: relative;
          background: transparent;
            overflow: visible;
          z-index: 1;
        ">
          
          <!-- Card Header with PixiCards Logo -->
          <div style="
            background: linear-gradient(90deg, #ec4899, #a855f7, #3b82f6);
            padding: 16px 24px;
            position: relative;
            overflow: visible;
          ">
            <!-- Bleed extension for header -->
            <div style="
              position: absolute;
              top: -64px;
              left: -128px;
              right: -128px;
              bottom: 0;
              background: linear-gradient(90deg, #ec4899, #a855f7, #3b82f6);
              z-index: 0;
            "></div>
            <!-- Dots removed to match leaderboard style -->
            
          <div style="
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1;
          ">
            <span style="
              font-size: 26px;
              font-weight: 900;
              color: white;
              letter-spacing: 0.06em;
              filter: drop-shadow(0 4px 3px rgba(0, 0, 0, 0.07));
            ">PIXICARDS</span>
          </div>
          </div>

          <!-- Card Content -->
          <div style="padding: 12px;">
            <!-- Character Name Header -->
            <div style="
              text-align: center;
              margin-bottom: 12px;
              background: linear-gradient(90deg, #fef3c7, #fce7f3, #ddd6fe);
              border-radius: 8px;
              padding: 8px;
              border: 2px solid #fde047;
              box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            ">
              <h3 style="
                font-size: 16px;
                font-weight: 900;
                color: #581c87;
                letter-spacing: 0.025em;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                margin: 0;
              ">
                ${name || "🌟 Magical Friend 🌟"}
              </h3>
            </div>

            <!-- Character Image -->
            <div style="position: relative; margin-bottom: 16px;">
              <div style="
                width: 380px;
                height: 380px;
                margin: 0 auto;
                display: flex;
                align-items: center;
                justify-content: center;
              ">
                <img src="${imageUrl}" alt="Character" style="
                  width: 380px;
                  height: 380px;
                  object-fit: cover;
                  border-radius: 0;
                  filter: contrast(1.1) saturate(1.05);
                  -webkit-print-color-adjust: exact;
                  color-adjust: exact;
                " />
              </div>
            </div>

            ${(powerStat || magicStat || speedStat) ? `
            <!-- Stats Display -->
            <div style="
              background: linear-gradient(90deg, #ffffff, #dbeafe);
              border: 2px solid #3b82f6;
              border-radius: 8px;
              padding: 12px;
              margin-bottom: 12px;
              box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            ">
              <div style="
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 0;
              ">
                ${powerStat ? `
                <div style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  padding: 0 16px;
                  border-right: 1px solid #d1d5db;
                ">
                  <div style="
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    margin-bottom: 4px;
                  ">
                    <span style="color: #ef4444;">⭐</span>
                    <span style="
                      font-size: 12px;
                      font-weight: 500;
                      color: #dc2626;
                    ">Power</span>
                  </div>
                  <span style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #374151;
                  ">${powerStat}</span>
                </div>
                ` : ''}
                ${magicStat ? `
                <div style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  padding: 0 16px;
                  ${speedStat ? 'border-right: 1px solid #d1d5db;' : ''}
                ">
                  <div style="
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    margin-bottom: 4px;
                  ">
                    <span style="color: #a855f7;">🔮</span>
                    <span style="
                      font-size: 12px;
                      font-weight: 500;
                      color: #9333ea;
                    ">Magic</span>
                  </div>
                  <span style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #374151;
                  ">${magicStat}</span>
                </div>
                ` : ''}
                ${speedStat ? `
                <div style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  padding: 0 16px;
                ">
                  <div style="
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    margin-bottom: 4px;
                  ">
                    <span style="color: #3b82f6;">💨</span>
                    <span style="
                      font-size: 12px;
                      font-weight: 500;
                      color: #2563eb;
                    ">Speed</span>
                  </div>
                  <span style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #374151;
                  ">${speedStat}</span>
                </div>
                ` : ''}
              </div>
            </div>
            ` : ''}

            <!-- Character Story Box -->
            <div style="
              background: linear-gradient(135deg, white, #dbeafe);
              border: 2px solid #bfdbfe;
              border-radius: 8px;
              padding: 8px;
              margin-bottom: 8px;
              box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            ">
              <p style="
                font-size: 12px;
                color: #581c87;
                text-align: center;
                line-height: 1.5;
                background: white;
                border-radius: 8px;
                padding: 8px;
                border: 1px solid #e0e7ff;
                margin: 0;
                white-space: nowrap;
                overflow: hidden;
              ">
                ${description || "This magical friend loves adventures and spreading joy! ✨🌈"}
              </p>
            </div>

            <!-- Character Story Box -->

            <!-- Creator Footer -->
            <div style="
              position: relative;
              overflow: visible;
              background: linear-gradient(90deg, #7c3aed, #ec4899, #f97316);
              color: white;
              border-radius: 8px;
              padding: 8px;
              text-align: center;
              box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            ">
              <!-- Bleed extension for footer -->
              <div style="
                position: absolute;
                top: 0;
                left: -140px;
                right: -140px;
                bottom: -64px;
                background: linear-gradient(90deg, #7c3aed, #ec4899, #f97316);
                z-index: 0;
              "></div>
              <div style="
                position: relative;
                font-size: 12px;
                font-weight: bold;
                color: white;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
                z-index: 1;
              ">
                ${creator ? `Created by ${creator}` : "✨ Magical Character ✨"}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// React component wrapper for display (not used in download)
export function PrintCharacterCard(props: PrintCharacterCardProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const logDebugInfo = () => {
      if (containerRef.current) {
        const imageContainer = containerRef.current.querySelector('#character-image-container') as HTMLElement;
        const debugWrapper = imageContainer?.parentElement as HTMLElement;
        
        if (imageContainer && debugWrapper) {
          console.log('=== CHARACTER IMAGE DEBUG INFO ===');
          console.log('Debug wrapper (red) BoundingClientRect:', debugWrapper.getBoundingClientRect());
          console.log('Image container (green) BoundingClientRect:', imageContainer.getBoundingClientRect());
          
          const computedStyle = window.getComputedStyle(imageContainer);
          console.log('Applied background-size:', computedStyle.backgroundSize);
          console.log('Applied background-position:', computedStyle.backgroundPosition);
          console.log('Applied background-image:', computedStyle.backgroundImage.substring(0, 100) + '...');
          console.log('Container width:', imageContainer.offsetWidth);
          console.log('Container height:', imageContainer.offsetHeight);
          console.log('===================================');
        }
      }
    };

    // Log immediately and after a short delay to ensure rendering is complete
    logDebugInfo();
    const timer = setTimeout(logDebugInfo, 100);
    
    return () => clearTimeout(timer);
  }, [props.imageUrl]);

  // Note: This component is specifically for print generation and needs raw HTML
  // It should only be used in trusted admin contexts for card printing
  return (
    <div ref={containerRef} dangerouslySetInnerHTML={{ __html: createPrintCardHTML(props) }} />
  );
}