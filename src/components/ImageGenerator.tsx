
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Wand2 } from "lucide-react";
import { PlayingCardStar } from "./ui/playing-card-star";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { VoiceInput } from "./VoiceInput";
import { SimpleLoadingScreen } from "./SimpleLoadingScreen";

interface ImageGeneratorProps {
  onImageGenerated: (imageUrl: string, prompt: string, signature?: string, imageDescription?: string) => void;
  initialPrompt?: string;
}

const followUpSuggestions = [
  "add a magical hat",
  "put them on a fluffy cloud",
  "give them sparkly wings",
  "add a rainbow background",
  "put them in a castle",
  "add some friendly animal friends"
];

const promptGuardrails = [
  "cartoon style",
  "kid-friendly",
  "colorful",
  "magical",
  "cute",
  "friendly"
];

export function ImageGenerator({ onImageGenerated, initialPrompt = "" }: ImageGeneratorProps) {
  const [prompt, setPrompt] = useState(typeof initialPrompt === 'string' ? initialPrompt : "");
  const [signature, setSignature] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const { toast } = useToast();

  // Update prompt when initialPrompt changes
  useEffect(() => {
    const newPrompt = typeof initialPrompt === 'string' ? initialPrompt : "";
    setPrompt(newPrompt);
  }, [initialPrompt]);

  const handleVoiceTranscript = (text: string) => {
    setPrompt(typeof text === 'string' ? text : "");
  };


  const enhancePrompt = async (userPrompt: string): Promise<string> => {
    // Simple enhancement: just pass user prompt through
    // The edge function now handles all prompt processing
    return userPrompt;
  };

  const generateImage = async () => {
    const promptText = typeof prompt === 'string' ? prompt : "";
    console.log('🔍 Image generation started:', { promptText, isMobile: window.innerWidth < 768 });

    if (!promptText.trim()) {
      toast({
        title: "Prompt Required",
        description: "Please describe what you'd like to create!",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setIsUpgrading(false);
    
    try {
      const enhancedPrompt = await enhancePrompt(promptText);
      console.log('✨ Enhanced prompt ready:', enhancedPrompt);
      
      const baseRequest: any = { prompt: enhancedPrompt, aspect: 'portrait', transparentBackground: false };
      
      // Fast preview only
      console.log('📡 Calling generate-image (fast preview)...');
      const { data: fastData, error: fastError } = await supabase.functions.invoke('generate-image', {
        body: { ...baseRequest, fast: true }
      });

      if (fastError) throw new Error(fastError.message);
      if (fastData?.error) throw new Error(fastData.error);

      console.log('✅ Preview image generated:', fastData.imageUrl);
      onImageGenerated(fastData.imageUrl, promptText, signature, fastData.imageDescription);
      setIsGenerating(false);
      setIsUpgrading(false);
      toast({ title: "Preview ready", description: "Save to add it to the leaderboard or edit more." });
      
    } catch (error) {
      console.error('💥 Error generating image:', error);
      setIsGenerating(false);
      setIsUpgrading(false);
      toast({
        title: "Oops! Magic Didn't Work",
        description: "Something went wrong while creating your character. Please try again!",
        variant: "destructive"
      });
    }
  };

  const addFollowUp = (suggestion: string) => {
    const currentPrompt = typeof prompt === 'string' ? prompt : "";
    const suggestionText = typeof suggestion === 'string' ? suggestion : "";
    setPrompt(`${currentPrompt}, ${suggestionText}`);
  };


  const promptLength = (typeof prompt === 'string' ? prompt : "").length;

  return (
    <>
      <SimpleLoadingScreen isVisible={isGenerating} />
      
      <div className="w-full max-w-2xl mx-auto space-y-6">
        {/* Main Generator */}
        <div className="bg-card-gradient rounded-3xl p-4 sm:p-6 shadow-magical border border-border">
          <div className="text-center mb-4 sm:mb-6">
            <PlayingCardStar className="w-8 h-8 sm:w-10 sm:h-10 text-accent mx-auto mb-3 animate-sparkle" />
            <h2 className="text-xl sm:text-2xl font-bold text-foreground">Create Your PixiCard</h2>
            <p className="text-sm sm:text-base text-muted-foreground px-2">Describe your magical character and watch them come to life!</p>
          </div>

            <div className="space-y-4">
            <div className="relative">
              <Textarea
                value={typeof prompt === 'string' ? prompt : ''}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Describe your magical character... (e.g., a friendly dragon with rainbow scales)"
                className="min-h-[80px] sm:min-h-[100px] border-2 border-accent/30 focus:border-accent rounded-xl resize-none text-base sm:text-lg pr-16"
                maxLength={200}
              />
              <div className="absolute top-2 right-2">
                <VoiceInput 
                  onTranscript={handleVoiceTranscript}
                  onAIGenerate={() => {}}
                  disabled={isGenerating}
                  showAIWand={false}
                />
              </div>
            </div>

            <div className="flex justify-end">
              <div className="text-xs text-muted-foreground">
                {promptLength}/200 characters
              </div>
            </div>

            <div className="relative">
              <input
                type="text"
                value={signature}
                onChange={(e) => setSignature(e.target.value)}
                placeholder="Sign your card (e.g., Arianna from London)"
                className="w-full border-2 border-accent/30 focus:border-accent rounded-xl px-4 py-3 text-base sm:text-lg"
                maxLength={50}
              />
              <div className="text-xs text-muted-foreground text-right mt-1">
                {signature.length}/50 characters
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                onClick={generateImage}
                disabled={isGenerating || !(typeof prompt === 'string' ? prompt : "").trim()}
                className="bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold px-6 sm:px-8 py-3 rounded-full disabled:opacity-50"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 mr-2 animate-spin" />
                    <span className="text-sm sm:text-base">Creating Magic...</span>
                  </>
                ) : (
                  <>
                    <Wand2 className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                    <span className="text-sm sm:text-base">Generate Character</span>
                  </>
                )}
              </Button>
            </div>
          </div>

        </div>
      </div>
    </>
  );
}
